package resolvers

import (
	"context"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/reward"
)

type ClaimResolver struct {
	claimService service.ClaimI
}

func NewClaimResolver() *ClaimResolver {
	return &ClaimResolver{
		claimService: reward.NewClaimService(),
	}
}

func (r *ClaimResolver) GetClaimReward(ctx context.Context) (*gql_model.ClaimRewardResponse, error) {
	userID := GetUserIDFromContext(ctx)
	if userID == uuid.Nil {
		return &gql_model.ClaimRewardResponse{
			ClaimActivityCashback: "",
			ClaimAgentReferral:    "",
		}, nil
	}

	// Validate input
	if userID == uuid.Nil {
		return &gql_model.ClaimRewardResponse{
			ClaimActivityCashback: "",
			ClaimAgentReferral:    "",
		}, nil
	}

	claimReward, err := r.claimService.GetClaimReward(ctx, userID)
	if err != nil {
		return &gql_model.ClaimRewardResponse{
			ClaimActivityCashback: "",
			ClaimAgentReferral:    "",
		}, nil
	}
	return &gql_model.ClaimRewardResponse{
		ClaimActivityCashback: claimReward.ClaimMeme,
		ClaimAgentReferral:    claimReward.ClaimContract,
		TotalClaimedUsd:       claimReward.TotalClaimedUsd,
	}, nil
}

// ClaimActivityCashback handles activity cashback claiming
func (r *ClaimResolver) ClaimActivityCashback(ctx context.Context, input gql_model.ClaimActivityCashbackInput) (*gql_model.ClaimResultResponse, error) {
	userID := GetUserIDFromContext(ctx)
	if userID == uuid.Nil {
		return &gql_model.ClaimResultResponse{
			Success: false,
			Message: "User is not logged in",
		}, nil
	}

	// Validate input
	if userID == uuid.Nil {
		return &gql_model.ClaimResultResponse{
			Success: false,
			Message: "Invalid user ID",
		}, nil
	}

	err := r.claimService.ClaimMemeReward(ctx, userID, input.ClaimAddress)
	if err != nil {
		return &gql_model.ClaimResultResponse{
			Success: false,
			Message: "failed to claim activity cashback",
		}, nil
	}

	return &gql_model.ClaimResultResponse{
		Success: true,
		Message: "Activity cashback claimed successfully",
	}, nil
}

func (r *ClaimResolver) ClaimAgentReferral(ctx context.Context, input gql_model.ClaimAgentReferralInput) (*gql_model.ClaimResultResponse, error) {
	userID := GetUserIDFromContext(ctx)
	if userID == uuid.Nil {
		return &gql_model.ClaimResultResponse{
			Success: false,
			Message: "User is not logged in",
		}, nil
	}

	// Validate input based on reward type
	switch input.RewardType {
	case "MEME":
		if input.ClaimAddress == nil || *input.ClaimAddress == "" {
			return &gql_model.ClaimResultResponse{
				Success: false,
				Message: "Claim address is required for MEME rewards",
			}, nil
		}
		err := r.claimService.ClaimMemeReward(ctx, userID, *input.ClaimAddress)
		if err != nil {
			return &gql_model.ClaimResultResponse{
				Success: false,
				Message: "Failed to claim MEME rewards",
			}, nil
		}
		return &gql_model.ClaimResultResponse{
			Success: true,
			Message: "MEME rewards claimed successfully",
		}, nil

	case "CONTRACT":
		err := r.claimService.ClaimContractReward(ctx, userID)
		if err != nil {
			return &gql_model.ClaimResultResponse{
				Success: false,
				Message: "Failed to claim CONTRACT rewards",
			}, nil
		}
		return &gql_model.ClaimResultResponse{
			Success: true,
			Message: "CONTRACT rewards claimed successfully",
		}, nil

	case "ALL":
		// 一键领取所有奖励
		if input.ClaimAddress == nil || *input.ClaimAddress == "" {
			return &gql_model.ClaimResultResponse{
				Success: false,
				Message: "Claim address is required for claiming all rewards",
			}, nil
		}
		err := r.claimService.ClaimAllRewards(ctx, userID, *input.ClaimAddress)
		if err != nil {
			return &gql_model.ClaimResultResponse{
				Success: false,
				Message: "Failed to claim all rewards",
			}, nil
		}
		return &gql_model.ClaimResultResponse{
			Success: true,
			Message: "All rewards claimed successfully",
		}, nil

	default:
		return &gql_model.ClaimResultResponse{
			Success: false,
			Message: "Invalid reward type",
		}, nil
	}
}
