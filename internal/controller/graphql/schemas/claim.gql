# Claim related schemas

enum RewardType {
  MEME
  CONTRACT
  ALL
}

input ClaimActivityCashbackInput {
  "Address of the user to claim the cashback"
  claimAddress: String!
}

input ClaimAgentReferralInput {
  rewardType: RewardType!
  claimAddress: String
}

type ClaimRewardResponse {
  claimActivityCashback: String!
  claimAgentReferral: String!
  totalClaimedUsd: String!
}

type ClaimResultResponse {
  success: Boolean!
  message: String!
}
