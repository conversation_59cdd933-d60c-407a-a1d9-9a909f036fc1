type InvitationRecord {
  address: String!
  transactionVolume: Float!
  invitedWithdrawal: Float!
  date: String!
}

input InvitationRecordRequest {
  page: Int!
  pageSize: Int!
}

type InvitationRecordResponse {
  success: Boolean!
  message: String!
  data: [InvitationRecord!]!
  total: Int!
  page: Int!
  pageSize: Int!
}

# WithdrawalRecord represents a single withdrawal record
type WithdrawalRecord {
  hash: String!
  withdrawalReward: String!
  date: String!
}

input WithdrawalRecordRequest {
  page: Int!
  pageSize: Int!
}

# WithdrawalRecordResponse represents the response for withdrawal records query
type WithdrawalRecordResponse {
  data: [WithdrawalRecord!]!
  total: Int!
  page: Int!
  pageSize: Int!
  success: Boolean!
  message: String
}

# RewardClaimHistoryRecord represents a single reward claim record
type RewardClaimHistoryRecord {
  id: ID!
  userId: ID!
  address: String!
  amount: String!
  amountUsd: String!
  token: String!
  chainId: Int!
  type: String!
  result: String!
  transactionHash: String
  onchainTimestamp: String
  errorMessage: String
  errorCode: String
  processedAt: String
  createdAt: String!
  updatedAt: String!
}

input RewardClaimHistoryRequest {
  page: Int!
  pageSize: Int!
  type: String
}

# RewardClaimHistoryResponse represents the response for reward claim history query
type RewardClaimHistoryResponse {
  data: [RewardClaimHistoryRecord!]!
  total: Int!
  page: Int!
  pageSize: Int!
  success: Boolean!
  message: String
}
