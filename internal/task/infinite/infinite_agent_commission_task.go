package infinite

import (
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// InfiniteAgentCommissionTask Infinite agent commission calculation task
type InfiniteAgentCommissionTask struct{}

// NewInfiniteAgentCommissionTask Create a new infinite agent commission calculation task instance
func NewInfiniteAgentCommissionTask() *InfiniteAgentCommissionTask {
	return &InfiniteAgentCommissionTask{}
}

// CalculateInfiniteAgentCommissions Calculate infinite agent commissions
// Executed daily at 00:05, calculates commissions for all active infinite agents
func (t *InfiniteAgentCommissionTask) CalculateInfiniteAgentCommissions() {
	global.GVA_LOG.Info("Starting infinite agent commission calculation task")

	// Get the previous day's time as the calculation date
	calculationDate := time.Now().UTC().AddDate(0, 0, -1)
	calculationDateStr := calculationDate.Format("2006-01-02 15:04:05")

	global.GVA_LOG.Info("Calculation date", zap.String("date", calculationDateStr))

	// Get all active infinite agent configurations
	activeInfiniteAgents, err := t.GetActiveInfiniteAgentConfigs()
	if err != nil {
		global.GVA_LOG.Error("Failed to get active infinite agents", zap.Error(err))
		return
	}

	global.GVA_LOG.Info("Found active infinite agents", zap.Int("infinite_agent_count", len(activeInfiniteAgents)))

	processedCount := 0
	errorCount := 0

	for _, infiniteAgent := range activeInfiniteAgents {
		if err := t.ProcessInfiniteAgentCommission(infiniteAgent); err != nil {
			global.GVA_LOG.Error("Failed to process infinite agent commission calculation",
				zap.String("infinite_agent_user_id", infiniteAgent.UserID.String()),
				zap.Error(err))
			errorCount++
		} else {
			processedCount++
		}
	}

	global.GVA_LOG.Info("Infinite agent commission calculation task completed",
		zap.String("date", calculationDateStr),
		zap.Int("total_infinite_agents", len(activeInfiniteAgents)),
		zap.Int("processed_count", processedCount),
		zap.Int("error_count", errorCount))
}

// GetActiveInfiniteAgentConfigs Get all infinite agent configurations with ACTIVE status
func (t *InfiniteAgentCommissionTask) GetActiveInfiniteAgentConfigs() ([]model.InfiniteAgentConfig, error) {
	var infiniteAgents []model.InfiniteAgentConfig

	err := global.GVA_DB.Debug().Where("status = ?", "ACTIVE").Find(&infiniteAgents).Error
	if err != nil {
		return nil, fmt.Errorf("failed to query active infinite agents: %w", err)
	}

	return infiniteAgents, nil
}

// ProcessInfiniteAgentCommission Process commission calculation for a single infinite agent
func (t *InfiniteAgentCommissionTask) ProcessInfiniteAgentCommission(infiniteAgent model.InfiniteAgentConfig) error {
	global.GVA_LOG.Debug("Starting infinite agent commission calculation",
		zap.String("infinite_agent_user_id", infiniteAgent.UserID.String()))

	// Calculate infinite agent tree commissions
	commissionData, err := t.calculateInfiniteAgentTreeCommission(infiniteAgent.UserID)
	if err != nil {
		return fmt.Errorf("failed to calculate infinite agent tree commissions: %w", err)
	}

	// Calculate final commission amount according to the correct formula:
	// 总净手续费 = 总手续费 - 已支付的总佣金
	// 最终佣金 = 总净手续费 × 佣金率N

	// Calculate total net fee: total fees - total paid commissions
	totalNetFeeUSD := commissionData.TotalNetFeeUSD
	if totalNetFeeUSD.IsNegative() {
		global.GVA_LOG.Warn("Detected negative net fee, setting to 0",
			zap.String("infinite_agent_user_id", infiniteAgent.UserID.String()),
			zap.String("original_net_fee_usd", commissionData.TotalNetFeeUSD.String()))
		totalNetFeeUSD = decimal.Zero
	}

	// Calculate final commission: total net fee * commission rate
	finalCommissionAmount := totalNetFeeUSD.Mul(infiniteAgent.CommissionRateN)

	// Update infinite agent configuration commission data
	err = t.updateInfiniteAgentCommissionData(infiniteAgent.ID, commissionData, finalCommissionAmount)
	if err != nil {
		return fmt.Errorf("failed to update infinite agent commission data: %w", err)
	}

	global.GVA_LOG.Debug("Infinite agent commission calculation completed",
		zap.String("infinite_agent_user_id", infiniteAgent.UserID.String()),
		zap.String("total_net_fee_usd", totalNetFeeUSD.String()),
		zap.String("final_commission_amount_usd", finalCommissionAmount.String()),
		zap.String("commission_rate_n", infiniteAgent.CommissionRateN.String()))

	return nil
}

// InfiniteAgentCommissionData Infinite agent commission data structure
type InfiniteAgentCommissionData struct {
	TotalNetFeeUSD decimal.Decimal

	// Detailed breakdown
	MemeTotalFeeUSD           decimal.Decimal
	MemePaidCommissionUSD     decimal.Decimal
	MemeNetFeeUSD             decimal.Decimal
	ContractTotalFeeUSD       decimal.Decimal
	ContractPaidCommissionUSD decimal.Decimal
	ContractNetFeeUSD         decimal.Decimal

	// Tree statistics
	TreeTotalNodes   int
	TreeActiveUsers  int
	TreeTradingUsers int
	TreeMaxDepth     int
	TreeDirectCount  int

	// Volume statistics
	TotalVolumeUSD    decimal.Decimal
	MemeVolumeUSD     decimal.Decimal
	ContractVolumeUSD decimal.Decimal
}

// calculateInfiniteAgentTreeCommission Calculate infinite agent tree commissions
func (t *InfiniteAgentCommissionTask) calculateInfiniteAgentTreeCommission(infiniteAgentUserID uuid.UUID) (*InfiniteAgentCommissionData, error) {
	global.GVA_LOG.Debug("Starting infinite agent tree commission calculation",
		zap.String("infinite_agent_user_id", infiniteAgentUserID.String()))

	// Find the referral tree snapshot where the infinite agent user is located
	var snapshot model.InfiniteAgentReferralTree
	err := global.GVA_DB.Debug().Debug().Where("infinite_agent_user_id = ? AND status = 'ACTIVE'", infiniteAgentUserID).
		First(&snapshot).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			global.GVA_LOG.Warn("Referral tree snapshot not found for infinite agent user",
				zap.String("infinite_agent_user_id", infiniteAgentUserID.String()))
			return &InfiniteAgentCommissionData{
				TotalNetFeeUSD: decimal.Zero,
			}, nil
		}
		return nil, fmt.Errorf("failed to query referral tree snapshot: %w", err)
	}

	global.GVA_LOG.Debug("Found referral tree snapshot",
		zap.String("infinite_agent_user_id", infiniteAgentUserID.String()),
		zap.Uint("snapshot_id", snapshot.ID))

	// Get all node user IDs under this tree
	var treeNodeUserIDs []uuid.UUID
	err = global.GVA_DB.Model(&model.InfiniteAgentTreeNode{}).
		Where("tree_id = ?", snapshot.ID).
		Pluck("user_id", &treeNodeUserIDs).Error
	if err != nil {
		return nil, fmt.Errorf("failed to query referral tree node user IDs: %w", err)
	}

	// Deduplicate user IDs to avoid duplicate calculations
	treeNodeUserIDs = t.removeDuplicateUserIDs(treeNodeUserIDs)

	if len(treeNodeUserIDs) == 0 {
		global.GVA_LOG.Warn("No node users under referral tree",
			zap.String("infinite_agent_user_id", infiniteAgentUserID.String()),
			zap.Uint("snapshot_id", snapshot.ID))
		return &InfiniteAgentCommissionData{
			TotalNetFeeUSD: decimal.Zero,
		}, nil
	}

	global.GVA_LOG.Debug("Retrieved tree node users",
		zap.String("infinite_agent_user_id", infiniteAgentUserID.String()),
		zap.Int("user_count", len(treeNodeUserIDs)))

	// Calculate detailed meme fee breakdown
	memeTotalFeeUSD, memePaidCommissionUSD, err := t.calculateMemeFeeBreakdown(treeNodeUserIDs)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate meme fee breakdown: %w", err)
	}
	memeNetFeeUSD := memeTotalFeeUSD.Sub(memePaidCommissionUSD)
	if memeNetFeeUSD.IsNegative() {
		memeNetFeeUSD = decimal.Zero
	}

	// Calculate detailed contract fee breakdown
	contractTotalFeeUSD, contractPaidCommissionUSD, err := t.calculateContractFeeBreakdown(treeNodeUserIDs)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate contract fee breakdown: %w", err)
	}
	contractNetFeeUSD := contractTotalFeeUSD.Sub(contractPaidCommissionUSD)
	if contractNetFeeUSD.IsNegative() {
		contractNetFeeUSD = decimal.Zero
	}

	// Add contract net value and meme net value = total net value
	totalNetFeeUSD := memeNetFeeUSD.Add(contractNetFeeUSD)

	// Calculate tree statistics
	treeStats, err := t.calculateTreeStatistics(snapshot.ID, treeNodeUserIDs)
	if err != nil {
		global.GVA_LOG.Warn("Failed to calculate tree statistics, using defaults",
			zap.String("infinite_agent_user_id", infiniteAgentUserID.String()),
			zap.Error(err))
		treeStats = &TreeStatistics{
			TotalNodes:   len(treeNodeUserIDs),
			ActiveUsers:  len(treeNodeUserIDs),
			TradingUsers: len(treeNodeUserIDs),
			MaxDepth:     3,
			DirectCount:  len(treeNodeUserIDs),
		}
	}

	// Calculate volume statistics
	volumeStats, err := t.calculateVolumeStatistics(treeNodeUserIDs)
	if err != nil {
		global.GVA_LOG.Warn("Failed to calculate volume statistics, using defaults",
			zap.String("infinite_agent_user_id", infiniteAgentUserID.String()),
			zap.Error(err))
		volumeStats = &VolumeStatistics{
			TotalVolumeUSD:    decimal.Zero,
			MemeVolumeUSD:     decimal.Zero,
			ContractVolumeUSD: decimal.Zero,
		}
	}

	global.GVA_LOG.Debug("Detailed commission calculation completed",
		zap.String("infinite_agent_user_id", infiniteAgentUserID.String()),
		zap.String("meme_total_fee_usd", memeTotalFeeUSD.String()),
		zap.String("meme_paid_commission_usd", memePaidCommissionUSD.String()),
		zap.String("meme_net_fee_usd", memeNetFeeUSD.String()),
		zap.String("contract_total_fee_usd", contractTotalFeeUSD.String()),
		zap.String("contract_paid_commission_usd", contractPaidCommissionUSD.String()),
		zap.String("contract_net_fee_usd", contractNetFeeUSD.String()),
		zap.String("total_net_fee_usd", totalNetFeeUSD.String()))

	return &InfiniteAgentCommissionData{
		TotalNetFeeUSD: totalNetFeeUSD,

		// Detailed breakdown
		MemeTotalFeeUSD:           memeTotalFeeUSD,
		MemePaidCommissionUSD:     memePaidCommissionUSD,
		MemeNetFeeUSD:             memeNetFeeUSD,
		ContractTotalFeeUSD:       contractTotalFeeUSD,
		ContractPaidCommissionUSD: contractPaidCommissionUSD,
		ContractNetFeeUSD:         contractNetFeeUSD,

		// Tree statistics
		TreeTotalNodes:   treeStats.TotalNodes,
		TreeActiveUsers:  treeStats.ActiveUsers,
		TreeTradingUsers: treeStats.TradingUsers,
		TreeMaxDepth:     treeStats.MaxDepth,
		TreeDirectCount:  treeStats.DirectCount,

		// Volume statistics
		TotalVolumeUSD:    volumeStats.TotalVolumeUSD,
		MemeVolumeUSD:     volumeStats.MemeVolumeUSD,
		ContractVolumeUSD: volumeStats.ContractVolumeUSD,
	}, nil
}

// updateInfiniteAgentCommissionData Update infinite agent configuration commission data
func (t *InfiniteAgentCommissionTask) updateInfiniteAgentCommissionData(
	configID uuid.UUID,
	commissionData *InfiniteAgentCommissionData,
	finalCommissionAmount decimal.Decimal,
) error {
	// Calculate final commission amount
	finalCommission := finalCommissionAmount

	// Calculate commission efficiency (final commission / total net fee)
	commissionEfficiency := decimal.Zero
	if !commissionData.TotalNetFeeUSD.IsZero() {
		commissionEfficiency = finalCommission.Div(commissionData.TotalNetFeeUSD)
	}

	// Update database with comprehensive data
	updates := map[string]interface{}{
		// Core commission data
		"total_net_fee_usd":           commissionData.TotalNetFeeUSD,
		"final_commission_amount_usd": finalCommission,

		// Detailed fee breakdown
		"meme_total_fee_usd":           commissionData.MemeTotalFeeUSD,
		"meme_paid_commission_usd":     commissionData.MemePaidCommissionUSD,
		"meme_net_fee_usd":             commissionData.MemeNetFeeUSD,
		"contract_total_fee_usd":       commissionData.ContractTotalFeeUSD,
		"contract_paid_commission_usd": commissionData.ContractPaidCommissionUSD,
		"contract_net_fee_usd":         commissionData.ContractNetFeeUSD,

		"updated_at": time.Now().UTC(),
	}

	err := global.GVA_DB.Model(&model.InfiniteAgentConfig{}).
		Where("id = ?", configID).
		Updates(updates).Error
	if err != nil {
		return fmt.Errorf("failed to update infinite agent commission data: %w", err)
	}

	global.GVA_LOG.Debug("Updated infinite agent commission data with detailed breakdown",
		zap.String("config_id", configID.String()),
		zap.String("final_commission_amount_usd", finalCommission.String()),
		zap.String("commission_efficiency", commissionEfficiency.String()),
		zap.Int("tree_total_nodes", commissionData.TreeTotalNodes),
		zap.String("total_volume_usd", commissionData.TotalVolumeUSD.String()))

	return nil
}

// TreeStatistics Tree statistics structure
type TreeStatistics struct {
	TotalNodes   int
	ActiveUsers  int
	TradingUsers int
	MaxDepth     int
	DirectCount  int
}

// VolumeStatistics Volume statistics structure
type VolumeStatistics struct {
	TotalVolumeUSD    decimal.Decimal
	MemeVolumeUSD     decimal.Decimal
	ContractVolumeUSD decimal.Decimal
}

// calculateMemeFeeBreakdown Calculate detailed meme fee breakdown
func (t *InfiniteAgentCommissionTask) calculateMemeFeeBreakdown(userIDs []uuid.UUID) (decimal.Decimal, decimal.Decimal, error) {
	// Input validation
	if err := t.validateUserIDs(userIDs); err != nil {
		return decimal.Zero, decimal.Zero, err
	}

	// Build placeholder strings
	placeholders := make([]string, len(userIDs))
	args := make([]interface{}, len(userIDs))
	for i, id := range userIDs {
		placeholders[i] = "?"
		args[i] = id
	}

	// 1. Calculate total meme fees
	var memeTotalFeeResult struct {
		TotalMemeFee decimal.Decimal `json:"total_meme_fee"`
	}

	memeTotalFeeQuery := fmt.Sprintf(`
		SELECT COALESCE(SUM(at.platform_fee), 0) as total_meme_fee
		FROM affiliate_transactions at
		WHERE at.user_id IN (%s)
	`, strings.Join(placeholders, ","))

	err := global.GVA_DB.Debug().Raw(memeTotalFeeQuery, args...).Scan(&memeTotalFeeResult).Error
	if err != nil {
		return decimal.Zero, decimal.Zero, fmt.Errorf("failed to query total meme fees: %w", err)
	}

	// 2. Calculate paid meme commissions
	var memePaidCommissionResult struct {
		TotalPaidCommission decimal.Decimal `json:"total_paid_commission"`
	}

	memePaidCommissionQuery := fmt.Sprintf(`
		SELECT COALESCE(SUM(mcl.commission_amount), 0) as total_paid_commission
		FROM meme_commission_ledger mcl
		WHERE mcl.source_user_id IN (%s)
	`, strings.Join(placeholders, ","))

	err = global.GVA_DB.Debug().Raw(memePaidCommissionQuery, args...).Scan(&memePaidCommissionResult).Error
	if err != nil {
		return decimal.Zero, decimal.Zero, fmt.Errorf("failed to query paid meme commissions: %w", err)
	}

	return memeTotalFeeResult.TotalMemeFee, memePaidCommissionResult.TotalPaidCommission, nil
}

// calculateContractFeeBreakdown Calculate detailed contract fee breakdown
func (t *InfiniteAgentCommissionTask) calculateContractFeeBreakdown(userIDs []uuid.UUID) (decimal.Decimal, decimal.Decimal, error) {
	// Input validation
	if err := t.validateUserIDs(userIDs); err != nil {
		return decimal.Zero, decimal.Zero, err
	}

	// Build placeholder strings
	placeholders := make([]string, len(userIDs))
	args := make([]interface{}, len(userIDs))
	for i, id := range userIDs {
		placeholders[i] = "?"
		args[i] = id
	}

	// 1. Calculate total contract fees
	var contractTotalFeeResult struct {
		TotalContractFee decimal.Decimal `json:"total_contract_fee"`
	}

	contractTotalFeeQuery := fmt.Sprintf(`
		SELECT COALESCE(SUM(COALESCE(hlt.build_fee, 0)), 0) as total_contract_fee
		FROM hyper_liquid_transactions hlt
		WHERE hlt.user_id IN (%s)
	`, strings.Join(placeholders, ","))

	err := global.GVA_DB.Debug().Raw(contractTotalFeeQuery, args...).Scan(&contractTotalFeeResult).Error
	if err != nil {
		return decimal.Zero, decimal.Zero, fmt.Errorf("failed to query total contract fees: %w", err)
	}

	// 2. Calculate paid contract commissions
	var contractPaidCommissionResult struct {
		TotalPaidCommission decimal.Decimal `json:"total_paid_commission"`
	}

	contractPaidCommissionQuery := fmt.Sprintf(`
		SELECT COALESCE(SUM(cl.commission_amount), 0) as total_paid_commission
		FROM commission_ledger cl
		WHERE cl.source_user_id IN (%s)
	`, strings.Join(placeholders, ","))

	err = global.GVA_DB.Raw(contractPaidCommissionQuery, args...).Scan(&contractPaidCommissionResult).Error
	if err != nil {
		return decimal.Zero, decimal.Zero, fmt.Errorf("failed to query paid contract commissions: %w", err)
	}

	return contractTotalFeeResult.TotalContractFee, contractPaidCommissionResult.TotalPaidCommission, nil
}

// calculateTreeStatistics Calculate tree statistics
func (t *InfiniteAgentCommissionTask) calculateTreeStatistics(treeID uint, userIDs []uuid.UUID) (*TreeStatistics, error) {
	// Get tree statistics from the snapshot
	var tree model.InfiniteAgentReferralTree
	err := global.GVA_DB.Where("id = ?", treeID).First(&tree).Error
	if err != nil {
		return nil, fmt.Errorf("failed to query tree statistics: %w", err)
	}

	return &TreeStatistics{
		TotalNodes:   tree.TotalNodes,
		ActiveUsers:  tree.ActiveUsers,
		TradingUsers: tree.TradingUsers,
		MaxDepth:     tree.MaxDepth,
		DirectCount:  tree.DirectCount,
	}, nil
}

// calculateVolumeStatistics Calculate volume statistics
func (t *InfiniteAgentCommissionTask) calculateVolumeStatistics(userIDs []uuid.UUID) (*VolumeStatistics, error) {
	// Input validation
	if err := t.validateUserIDs(userIDs); err != nil {
		return nil, err
	}

	// Build placeholder strings
	placeholders := make([]string, len(userIDs))
	args := make([]interface{}, len(userIDs))
	for i, id := range userIDs {
		placeholders[i] = "?"
		args[i] = id
	}

	// Calculate meme volume
	var memeVolumeResult struct {
		TotalVolume decimal.Decimal `json:"total_volume"`
	}

	memeVolumeQuery := fmt.Sprintf(`
		SELECT COALESCE(SUM(at.volume_usd), 0) as total_volume
		FROM affiliate_transactions at
		WHERE at.user_id IN (%s)
	`, strings.Join(placeholders, ","))

	err := global.GVA_DB.Raw(memeVolumeQuery, args...).Scan(&memeVolumeResult).Error
	if err != nil {
		return nil, fmt.Errorf("failed to query meme volume: %w", err)
	}

	// Calculate contract volume (simplified - would need actual volume calculation from HyperLiquid)
	var contractVolumeResult struct {
		TotalVolume decimal.Decimal `json:"total_volume"`
	}

	contractVolumeQuery := fmt.Sprintf(`
		SELECT COALESCE(SUM(COALESCE(hlt.size, 0) * COALESCE(hlt.avg_price, 0)), 0) as total_volume
		FROM hyper_liquid_transactions hlt
		WHERE hlt.user_id IN (%s)
	`, strings.Join(placeholders, ","))

	err = global.GVA_DB.Raw(contractVolumeQuery, args...).Scan(&contractVolumeResult).Error
	if err != nil {
		return nil, fmt.Errorf("failed to query contract volume: %w", err)
	}

	totalVolume := memeVolumeResult.TotalVolume.Add(contractVolumeResult.TotalVolume)

	return &VolumeStatistics{
		TotalVolumeUSD:    totalVolume,
		MemeVolumeUSD:     memeVolumeResult.TotalVolume,
		ContractVolumeUSD: contractVolumeResult.TotalVolume,
	}, nil
}

// validateUserIDs Validate the validity of user ID list
func (t *InfiniteAgentCommissionTask) validateUserIDs(userIDs []uuid.UUID) error {
	if len(userIDs) == 0 {
		return fmt.Errorf("user ID list cannot be empty")
	}

	for i, userID := range userIDs {
		if userID == uuid.Nil {
			return fmt.Errorf("user ID at position %d is empty", i+1)
		}
	}

	return nil
}

// removeDuplicateUserIDs Remove duplicate user IDs
func (t *InfiniteAgentCommissionTask) removeDuplicateUserIDs(userIDs []uuid.UUID) []uuid.UUID {
	if len(userIDs) == 0 {
		return userIDs
	}

	// Use map to record existing user IDs
	seen := make(map[uuid.UUID]bool)
	var uniqueUserIDs []uuid.UUID

	for _, userID := range userIDs {
		if !seen[userID] {
			seen[userID] = true
			uniqueUserIDs = append(uniqueUserIDs, userID)
		}
	}

	global.GVA_LOG.Debug("User ID deduplication completed",
		zap.Int("original_count", len(userIDs)),
		zap.Int("unique_count", len(uniqueUserIDs)))

	return uniqueUserIDs
}
